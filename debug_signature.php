<?php

/**
 * 调试海博签名问题
 */

// 模拟配置
$secret = 'your_secret_here'; // 请替换为实际的secret

// 第一个参数（报签名错误的）
$params1 = [
    "carrierMerchantId" => "HBT_YQS",
    "shopName" => "测试门店1002",
    "shopId" => "9870681",
    "shopLng" => 120205049,
    "category" => 100,
    "contactPhone" => "0575-87639585",
    "shopAddress" => "浙江省杭州市余杭区海创科技中心_519 ",
    "shopLat" => 30290127
];

// 第二个参数（签名正确的）
$params2 = [
    "carrierMerchantId" => "HBT_YQS",
    "category" => 100,
    "contactPhone" => "0575-87639585",
    "shopAddress" => "浙江省杭州市余杭区海创科技中心",
    "shopId" => "987068",
    "shopLat" => 30290126,
    "shopLng" => 120205048,
    "shopName" => "测试门店1002"
];

function generateSignature($params, $secret) {
    // 1. 过滤参数（排除sign和空值）
    $filteredParams = [];
    foreach ($params as $key => $value) {
        if ($key === 'sign' || $value === null || $value === '') {
            continue;
        }

        // 数组和对象转为JSON字符串
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }

        // 布尔值转换
        if (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        }

        $filteredParams[$key] = $value;
    }

    // 2. 按参数名字典顺序排序
    ksort($filteredParams);

    // 3. 拼接参数
    $signString = '';
    foreach ($filteredParams as $key => $value) {
        $signString .= $key . $value;
    }

    // 4. 加上secret前缀
    $signString = $secret . $signString;

    // 5. SHA1加密并转小写
    return strtolower(sha1($signString));
}

function debugSignature($params, $label) {
    global $secret;
    
    echo "\n=== {$label} ===\n";
    
    // 显示原始参数
    echo "原始参数:\n";
    foreach ($params as $key => $value) {
        echo "  {$key}: " . var_export($value, true) . "\n";
    }
    
    // 过滤参数
    $filteredParams = [];
    foreach ($params as $key => $value) {
        if ($key === 'sign' || $value === null || $value === '') {
            continue;
        }

        if (is_array($value) || is_object($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }

        if (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        }

        $filteredParams[$key] = $value;
    }
    
    // 排序
    ksort($filteredParams);
    
    echo "\n过滤并排序后的参数:\n";
    foreach ($filteredParams as $key => $value) {
        echo "  {$key}: " . var_export($value, true) . "\n";
    }
    
    // 拼接字符串
    $signString = '';
    foreach ($filteredParams as $key => $value) {
        $signString .= $key . $value;
    }
    
    echo "\n拼接后的字符串: {$signString}\n";
    
    // 加上secret
    $finalString = $secret . $signString;
    echo "加上secret后: {$finalString}\n";
    
    // 生成签名
    $signature = strtolower(sha1($finalString));
    echo "最终签名: {$signature}\n";
    
    return $signature;
}

// 分析两个参数的差异
echo "参数差异分析:\n";

echo "\n参数1独有或不同的字段:\n";
foreach ($params1 as $key => $value) {
    if (!isset($params2[$key]) || $params2[$key] !== $value) {
        echo "  {$key}: " . var_export($value, true);
        if (isset($params2[$key])) {
            echo " (参数2中为: " . var_export($params2[$key], true) . ")";
        }
        echo "\n";
    }
}

echo "\n参数2独有或不同的字段:\n";
foreach ($params2 as $key => $value) {
    if (!isset($params1[$key]) || $params1[$key] !== $value) {
        echo "  {$key}: " . var_export($value, true);
        if (isset($params1[$key])) {
            echo " (参数1中为: " . var_export($params1[$key], true) . ")";
        }
        echo "\n";
    }
}

// 生成签名
$signature1 = debugSignature($params1, "参数1（报错的）");
$signature2 = debugSignature($params2, "参数2（正确的）");

echo "\n=== 总结 ===\n";
echo "参数1签名: {$signature1}\n";
echo "参数2签名: {$signature2}\n";
echo "签名是否相同: " . ($signature1 === $signature2 ? '是' : '否') . "\n";

// 检查可能的问题
echo "\n=== 可能的问题 ===\n";

// 1. 检查shopAddress末尾空格
if (isset($params1['shopAddress']) && isset($params2['shopAddress'])) {
    $addr1 = $params1['shopAddress'];
    $addr2 = $params2['shopAddress'];
    
    echo "1. shopAddress比较:\n";
    echo "   参数1: '{$addr1}' (长度: " . strlen($addr1) . ")\n";
    echo "   参数2: '{$addr2}' (长度: " . strlen($addr2) . ")\n";
    echo "   是否相同: " . ($addr1 === $addr2 ? '是' : '否') . "\n";
    
    if ($addr1 !== $addr2) {
        echo "   差异: 参数1末尾有 '_519 '，参数2没有\n";
    }
}

// 2. 检查shopId
if (isset($params1['shopId']) && isset($params2['shopId'])) {
    echo "\n2. shopId比较:\n";
    echo "   参数1: '{$params1['shopId']}'\n";
    echo "   参数2: '{$params2['shopId']}'\n";
    echo "   是否相同: " . ($params1['shopId'] === $params2['shopId'] ? '是' : '否') . "\n";
}

// 3. 检查坐标
echo "\n3. 坐标比较:\n";
echo "   shopLat - 参数1: {$params1['shopLat']}, 参数2: {$params2['shopLat']}\n";
echo "   shopLng - 参数1: {$params1['shopLng']}, 参数2: {$params2['shopLng']}\n";

?>
